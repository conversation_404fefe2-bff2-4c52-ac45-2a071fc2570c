#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

pub mod jiamichuanshu;
mod luyou;

use rocket::{Build, Rocket};
use serde::{Deserialize, Serialize};

use luyou::luyou_guanliqii::luyou_guanliqii;
use luyou::luyoujiegouti_chuli::kuayu_fairing;

/// 服务器响应结构体
#[derive(Serialize, Deserialize)]
pub struct fuwuqi_xiangying {
    pub chenggong: bool,
    pub xiaoxi: String,
    pub shuju: Option<serde_json::Value>,
}

/// 构建 Rocket 应用（不带Redis）
pub fn goujian_fuwuqi() -> Rocket<Build> {
    // 创建路由管理器
    let luyou_guanli = luyou_guanliqii::new();

    rocket::build()
        .mount("/jiekou", luyou_guanliqii::get_suoyou_luyou())
        .attach(kuayu_fairing)
        .manage(luyou_guanli)
}

/// 构建 Rocket 应用（带Redis支持）
pub fn goujian_fuwuqi_with_redis(_redis_guanliqi: std::sync::Arc<crate::chushihua::shujukuxitong::redishujuku::redis_lianjie_guanli>) -> Rocket<Build> {
    // 创建路由管理器
    let luyou_guanli = luyou_guanliqii::new();

    rocket::build()
        .mount("/jiekou", luyou_guanliqii::get_suoyou_luyou())
        .attach(kuayu_fairing)
        .manage(luyou_guanli)
}
