#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use crate::fuwuqi::luyou::luyoujiegouti_chuli::mingwen_xiangying;
use crate::fuwuqi::luyou::{luyou_qingqiu_kaishi, luyou_qingqiu_wancheng, luyou_rizhi_xinxi};
use rocket::http::Status;
use rocket::serde::json::Json;
use rocket::{get, options, routes, Route};

/// 测试接口处理函数
#[get("/ceshijiekou")]
pub fn jiekou_ceshi_chuli() -> Json<mingwen_xiangying> {
    let jiekou_ming = jiekou_ceshi::get_miaoshu();
    let qingqiu_lujing = jiekou_ceshi::get_lujing();
    let qingqiu_fangfa = jiekou_ceshi::get_fangfa();
    let _jiekou_jieshao = jiekou_ceshi::get_jieshao();
    luyou_qingqiu_kaishi(jiekou_ming, qingqiu_lujing, qingqiu_fangfa);
    let kaishi_shijian = std::time::Instant::now();
    let xiangying = mingwen_xiangying::jiandan_chenggong(
        "欢迎使用小落RO仙境传说资料站".to_string()
    );

    let chuli_shijian = kaishi_shijian.elapsed().as_millis() as u64;
    luyou_qingqiu_wancheng(jiekou_ming, chuli_shijian, 200);
    luyou_rizhi_xinxi(jiekou_ming, "测试接口调用成功");
    Json(xiangying)
}
#[options("/ceshijiekou")]
pub fn jiekou_ceshi_yujian() -> Status {
    Status::Ok
}
pub struct jiekou_ceshi;
impl jiekou_ceshi {
    pub fn get_lujing() -> &'static str {
        "/ceshijiekou"
    }
    pub fn get_fangfa() -> &'static str {
        "GET"
    }
    pub fn get_miaoshu() -> &'static str {
        "测试接口 - 返回欢迎信息"
    }
    pub fn get_jieshao() -> &'static str {
        "这是一个测试接口，用于验证路由系统是否正常工作。返回欢迎使用小落RO仙境传说资料站的消息。"
    }
    pub fn get_routes() -> Vec<Route> {
        routes![jiekou_ceshi_chuli, jiekou_ceshi_yujian]
    }
}
